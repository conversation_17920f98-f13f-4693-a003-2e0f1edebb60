<?php $__env->startSection('content'); ?>
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <h1 class="text-2xl font-bold text-gray-900">Welcome, <?php echo e(Auth::user()->name); ?></h1>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">Your personal dashboard</p>
        </div>
        <div class="border-t border-gray-200">
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <div class="text-sm font-medium text-gray-500">Email</div>
                <div class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2"><?php echo e(Auth::user()->email); ?></div>
            </div>
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <div class="text-sm font-medium text-gray-500">Account created</div>
                <div class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <?php echo e(Auth::user()->created_at->format('F j, Y')); ?></div>
            </div>
        </div>
    </div>

    <div class="mt-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <!-- Quick Stats -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-indigo-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Purchases</dt>
                            <dd>
                                <div class="text-lg font-medium text-gray-900">Rp
                                    <?php echo e(number_format($totalPurchases ?? 0, 0, ',', '.')); ?></div>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="<?php echo e(route('user.purchases')); ?>" class="font-medium text-indigo-600 hover:text-indigo-500">View
                        all</a>
                </div>
            </div>
        </div>

        <!-- Cart -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-indigo-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Cart Items</dt>
                            <dd>
                                <div class="text-lg font-medium text-gray-900">
                                    <?php
                                        $cartCount = 0;
                                        $cart = \App\Models\Cart::where('user_id', Auth::id())->first();
                                        if ($cart) {
                                            $cartCount = $cart->items->count();
                                        }
                                    ?>
                                    <?php echo e($cartCount); ?>

                                </div>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="<?php echo e(route('cart.index')); ?>" class="font-medium text-indigo-600 hover:text-indigo-500">View
                        cart</a>
                </div>
            </div>
        </div>

        <!-- Course Progress -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-500 rounded-md p-3">
                        <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Course Progress</dt>
                            <dd>
                                <div class="text-lg font-medium text-gray-900"><?php echo e($completedCourses); ?>/<?php echo e($totalCourses); ?> Completed</div>
                                <?php if($inProgressCourses > 0): ?>
                                    <div class="text-sm text-gray-500"><?php echo e($inProgressCourses); ?> in progress</div>
                                <?php endif; ?>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                    <a href="<?php echo e(route('browse.courses')); ?>" class="font-medium text-indigo-600 hover:text-indigo-500">Browse
                        courses</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Progress Section -->
    <?php if($totalCourses > 0): ?>
        <div class="mt-8">
            <h2 class="text-lg font-medium text-gray-900">My Courses</h2>
            <div class="mt-4 bg-white shadow overflow-hidden sm:rounded-lg">
                <ul role="list" class="divide-y divide-gray-200">
                    <?php $__currentLoopData = $courseProgress; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $progress): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="px-4 py-4 sm:px-6">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-indigo-600 truncate">
                                        <?php echo e($progress->course->title); ?>

                                    </p>
                                    <div class="mt-2 flex items-center">
                                        <div class="flex-1">
                                            <div class="w-full bg-gray-200 rounded-full h-2">
                                                <div class="bg-purple-600 h-2 rounded-full" style="width: <?php echo e($progress->progress_percentage); ?>%"></div>
                                            </div>
                                        </div>
                                        <span class="ml-3 text-sm text-gray-500"><?php echo e(number_format($progress->progress_percentage, 0)); ?>%</span>
                                    </div>
                                </div>
                                <div class="ml-4 flex-shrink-0 flex flex-col items-end">
                                    <?php if($progress->is_completed): ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            Completed
                                        </span>
                                        <?php if($progress->completed_at): ?>
                                            <span class="text-xs text-gray-500 mt-1"><?php echo e($progress->completed_at->format('M d, Y')); ?></span>
                                        <?php endif; ?>
                                    <?php elseif($progress->progress_percentage > 0): ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            In Progress
                                        </span>
                                        <?php if($progress->last_accessed_at): ?>
                                            <span class="text-xs text-gray-500 mt-1">Last: <?php echo e($progress->last_accessed_at->format('M d')); ?></span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            Not Started
                                        </span>
                                    <?php endif; ?>
                                    <a href="<?php echo e(route('browse.courses.access', $progress->course)); ?>"
                                       class="mt-2 text-xs text-indigo-600 hover:text-indigo-500">
                                        <?php echo e($progress->is_completed ? 'Review' : 'Continue'); ?>

                                    </a>
                                </div>
                            </div>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        </div>
    <?php endif; ?>

    <div class="mt-8">
        <h2 class="text-lg font-medium text-gray-900">Recent Activity</h2>
        <div class="mt-4 bg-white shadow overflow-hidden sm:rounded-lg">
            <ul role="list" class="divide-y divide-gray-200">
                <?php $__empty_1 = true; $__currentLoopData = $recentActivity; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <li class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <p class="text-sm font-medium text-indigo-600 truncate">
                                Purchased <?php echo e($activity->item_name); ?>

                                <span class="text-xs text-gray-500">(<?php echo e($activity->item_type); ?>)</span>
                            </p>
                            <div class="ml-2 flex-shrink-0 flex">
                                <p
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Completed
                                </p>
                            </div>
                        </div>
                        <div class="mt-2 sm:flex sm:justify-between">
                            <div class="sm:flex">
                                <p class="flex items-center text-sm text-gray-500">
                                    <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"
                                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    Rp <?php echo e(number_format($activity->amount, 0, ',', '.')); ?>

                                </p>
                            </div>
                            <div class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                                        clip-rule="evenodd" />
                                </svg>
                                <p>
                                    <?php echo e($activity->created_at->format('M d, Y')); ?>

                                </p>
                            </div>
                        </div>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <li class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <p class="text-sm font-medium text-indigo-600 truncate">No recent activity</p>
                        </div>
                        <div class="mt-2 sm:flex sm:justify-between">
                            <div class="sm:flex">
                                <p class="flex items-center text-sm text-gray-500">
                                    Your recent activity will appear here
                                </p>
                            </div>
                        </div>
                    </li>
                <?php endif; ?>
            </ul>
            <?php if(count($recentActivity) > 0): ?>
                <div class="bg-gray-50 px-4 py-3 text-right sm:px-6">
                    <a href="<?php echo e(route('user.purchases')); ?>"
                        class="text-sm font-medium text-indigo-600 hover:text-indigo-500">
                        View all purchases <span aria-hidden="true">&rarr;</span>
                    </a>
                </div>
            <?php endif; ?>
        </div>



        <!-- Call to Action -->
        <div class="mt-16 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl p-8 text-center text-white">
            <h2 class="text-2xl font-bold mb-4">Ready to Start Selling Your Digital Products?</h2>
            <p class="mb-6 max-w-2xl mx-auto">Join thousands of creators who are earning by selling their digital products
                on Digitora.</p>
            <a href="<?php echo e(route('seller.apply')); ?>"
                class="inline-block bg-white text-purple-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors duration-300">
                Become a Seller Today
            </a>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user-dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/users/dashboard.blade.php ENDPATH**/ ?>