<?php $__env->startSection('title', 'Browse Stores - Digitora'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<div class="bg-gradient-to-r from-purple-600 to-pink-700 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
            <h1 class="text-4xl font-bold mb-4">Discover Amazing Stores</h1>
            <p class="text-xl text-purple-100 mb-8">Explore stores from verified Indonesian entrepreneurs</p>
            
            <!-- Search Bar -->
            <div class="max-w-2xl mx-auto">
                <form method="GET" action="<?php echo e(route('browse.stores')); ?>" class="flex">
                    <input type="text" 
                           name="search" 
                           value="<?php echo e(request('search')); ?>"
                           placeholder="Search stores..." 
                           class="flex-1 px-4 py-3 rounded-l-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500">
                    <button type="submit" 
                            class="bg-purple-800 hover:bg-purple-900 px-6 py-3 rounded-r-lg font-medium transition-colors">
                        Search
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Results -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Sidebar Filters -->
        <div class="lg:w-64 flex-shrink-0">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Filters</h3>
                
                <form method="GET" action="<?php echo e(route('browse.stores')); ?>" id="filter-form">
                    <input type="hidden" name="search" value="<?php echo e(request('search')); ?>">
                    
                    <!-- Category Filter -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select name="category" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500" onchange="document.getElementById('filter-form').submit()">
                            <option value="">All Categories</option>
                            <?php $__currentLoopData = $detailedCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->slug); ?>" <?php echo e(request('category') == $category->slug ? 'selected' : ''); ?>>
                                    <?php echo e($category->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    
                    <!-- Sort Filter -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                        <select name="sort" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500" onchange="document.getElementById('filter-form').submit()">
                            <option value="newest" <?php echo e(request('sort') == 'newest' ? 'selected' : ''); ?>>Newest</option>
                            <option value="products_count" <?php echo e(request('sort') == 'products_count' ? 'selected' : ''); ?>>Most Products</option>
                            <option value="name" <?php echo e(request('sort') == 'name' ? 'selected' : ''); ?>>Store Name</option>
                        </select>
                    </div>
                </form>
                
                <?php if(request()->hasAny(['search', 'category', 'sort'])): ?>
                    <a href="<?php echo e(route('browse.stores')); ?>" 
                       class="inline-flex items-center text-sm text-purple-600 hover:text-purple-800">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Clear Filters
                    </a>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Store Grid -->
        <div class="flex-1">
            <!-- Results Header -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-900">
                    <?php if(request('search')): ?>
                        Search Results for "<?php echo e(request('search')); ?>"
                    <?php else: ?>
                        All Stores
                    <?php endif; ?>
                </h2>
                <p class="text-gray-600"><?php echo e($stores->total()); ?> stores found</p>
            </div>
            
            <?php if(count($formattedStores) > 0): ?>
                <!-- Store Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                    <?php $__currentLoopData = $formattedStores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                            <!-- Store Header -->
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center">
                                    <!-- Store Logo -->
                                    <div class="w-16 h-16 rounded-lg overflow-hidden bg-gray-200 flex-shrink-0 mr-4">
                                        <?php if($store['logo']): ?>
                                            <img src="<?php echo e($store['logo']); ?>" alt="<?php echo e($store['name']); ?>" class="w-full h-full object-cover">
                                        <?php else: ?>
                                            <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-purple-500 to-pink-600">
                                                <span class="text-white font-bold text-lg"><?php echo e(substr($store['name'], 0, 1)); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Store Info -->
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-lg font-semibold text-gray-900 truncate">
                                            <a href="<?php echo e(route('store.show', $store['slug'])); ?>" class="hover:text-purple-600">
                                                <?php echo e($store['name']); ?>

                                            </a>
                                        </h3>
                                        <?php if($store['main_category']): ?>
                                            <p class="text-sm text-purple-600 font-medium"><?php echo e($store['main_category']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Store Content -->
                            <div class="p-6">
                                <!-- Description -->
                                <?php if($store['description']): ?>
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-3"><?php echo e($store['description']); ?></p>
                                <?php endif; ?>
                                
                                <!-- Store Stats -->
                                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                    <span><?php echo e($store['product_count']); ?> products</span>
                                    <?php if($selectedDetailedCategory && $store['category_product_count'] > 0): ?>
                                        <span><?php echo e($store['category_product_count']); ?> in <?php echo e($selectedDetailedCategory->name); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Store Owner -->
                                <div class="flex items-center mb-4">
                                    <div class="w-8 h-8 rounded-full overflow-hidden bg-gray-200 mr-3">
                                        <?php if($store['user_avatar']): ?>
                                            <img src="<?php echo e($store['user_avatar']); ?>" alt="<?php echo e($store['user_name']); ?>" class="w-full h-full object-cover">
                                        <?php else: ?>
                                            <div class="w-full h-full flex items-center justify-center bg-gray-300">
                                                <span class="text-xs font-medium text-gray-600"><?php echo e(substr($store['user_name'], 0, 1)); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-700"><?php echo e($store['user_name']); ?></span>
                                        <div class="text-xs text-gray-500">Store Owner</div>
                                    </div>
                                </div>
                                
                                <!-- Action Button -->
                                <div class="flex space-x-2">
                                    <a href="<?php echo e(route('store.show', $store['slug'])); ?>" 
                                       class="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium text-center transition-colors">
                                        Visit Store
                                    </a>
                                    <a href="<?php echo e(route('store.all-products', $store['slug'])); ?>" 
                                       class="flex-1 border border-purple-600 text-purple-600 hover:bg-purple-50 px-4 py-2 rounded-lg text-sm font-medium text-center transition-colors">
                                        View Products
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <!-- Pagination -->
                <div class="flex justify-center">
                    <?php echo e($stores->appends(request()->query())->links()); ?>

                </div>
            <?php else: ?>
                <!-- No Results -->
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No stores found</h3>
                    <p class="text-gray-600 mb-8">
                        We couldn't find any stores matching your criteria. Try adjusting your filters or browse all available stores.
                    </p>
                    <div class="mt-8">
                        <a href="<?php echo e(route('browse.stores')); ?>"
                           class="inline-flex items-center px-6 py-3 border border-transparent shadow-sm text-base font-medium rounded-lg text-white bg-purple-600 hover:bg-purple-700 transition-colors">
                            Browse All Stores
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.browse', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/browse/stores.blade.php ENDPATH**/ ?>