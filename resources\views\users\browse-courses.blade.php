@extends('layouts.user-dashboard')

@section('content')
<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-900">Browse Courses</h1>
    <p class="text-gray-600">Discover online courses designed by Indonesian entrepreneurs</p>
</div>

<!-- Filters and Course Content -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
    <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-900">Filter Courses</h2>
        <span class="text-sm text-gray-500">{{ $courses->total() }} courses found</span>
    </div>
    <form method="GET" action="{{ route('user.browse.courses') }}" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <!-- Search -->
            <div class="lg:col-span-2">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text"
                       id="search"
                       name="search"
                       value="{{ request('search') }}"
                       placeholder="Search courses..."
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            </div>

            <!-- Category Filter -->
            <div>
                <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select name="category" id="category" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Categories</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                            {{ $category->name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Difficulty Filter -->
            <div>
                <label for="difficulty" class="block text-sm font-medium text-gray-700 mb-1">Difficulty</label>
                <select name="difficulty" id="difficulty" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Levels</option>
                    <option value="beginner" {{ request('difficulty') == 'beginner' ? 'selected' : '' }}>Beginner</option>
                    <option value="intermediate" {{ request('difficulty') == 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                    <option value="advanced" {{ request('difficulty') == 'advanced' ? 'selected' : '' }}>Advanced</option>
                </select>
            </div>

            <!-- Sort -->
            <div>
                <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                <select name="sort" id="sort" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Newest</option>
                    <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>Most Popular</option>
                    <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                    <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                </select>
            </div>
        </div>

        <div class="flex justify-end space-x-3">
            @if(request()->hasAny(['search', 'category', 'difficulty', 'sort']))
                <a href="{{ route('user.browse.courses') }}" 
                   class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Clear Filters
                </a>
            @endif
            <button type="submit" 
                    class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                Apply Filters
            </button>
        </div>
    </form>
</div>

@if($courses->count() > 0)
    <!-- Course Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
        @foreach($courses as $course)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                <!-- Course Thumbnail -->
                <div class="aspect-video bg-gray-200 relative">
                    @if($course->thumbnail_url)
                        <img src="{{ $course->thumbnail_url }}" alt="{{ $course->title }}" class="w-full h-full object-cover">
                    @else
                        <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-indigo-500 to-purple-600">
                            <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                    @endif
                    
                    <!-- Difficulty Badge -->
                    @if($course->difficulty_level)
                        <div class="absolute top-2 left-2">
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                {{ $course->difficulty_level === 'beginner' ? 'bg-green-100 text-green-800' : '' }}
                                {{ $course->difficulty_level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                {{ $course->difficulty_level === 'advanced' ? 'bg-red-100 text-red-800' : '' }}">
                                {{ ucfirst($course->difficulty_level) }}
                            </span>
                        </div>
                    @endif
                </div>

                <!-- Course Content -->
                <div class="p-6">
                    <div class="flex items-center mb-2">
                        @if($course->seller && $course->seller->sellerApplication)
                            <div class="flex items-center text-sm text-gray-600">
                                <span>{{ $course->seller->sellerApplication->store_name }}</span>
                            </div>
                        @endif
                    </div>

                    <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                        <a href="{{ route('browse.courses.show', $course->slug) }}" class="hover:text-indigo-600 transition-colors">
                            {{ $course->title }}
                        </a>
                    </h3>

                    @if($course->short_description)
                        <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ $course->short_description }}</p>
                    @endif

                    <!-- Course Stats -->
                    <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                        <div class="flex items-center space-x-4">
                            <span>{{ $course->sections_count }} sections</span>
                            <span>{{ $course->curriculum_items_count }} lessons</span>
                        </div>
                    </div>

                    <!-- Price and Action -->
                    <div class="flex items-center justify-between">
                        <div class="text-lg font-bold text-gray-900">
                            Rp {{ number_format($course->price, 0, ',', '.') }}
                        </div>
                        @if(in_array($course->id, $purchasedCourseIds ?? []))
                            <a href="{{ route('browse.courses.show', $course->slug) }}"
                               class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                                Continue Learning
                            </a>
                        @else
                            <a href="{{ route('browse.courses.show', $course->slug) }}"
                               class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors">
                                View Course
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Pagination -->
    <div class="flex justify-center">
        {{ $courses->appends(request()->query())->links() }}
    </div>
@else
    <!-- Empty State -->
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No courses found</h3>
        <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
        <div class="mt-6">
            <a href="{{ route('user.browse.courses') }}" 
               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                Clear Filters
            </a>
        </div>
    </div>
@endif
@endsection
