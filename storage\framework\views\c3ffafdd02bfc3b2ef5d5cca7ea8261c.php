<?php
    use Illuminate\Support\Str;
?>

<?php $__env->startSection('content'); ?>
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">Browse Stores</h1>
        <p class="text-gray-600">Discover stores by category and find your favorite creators</p>
    </div>

    <div class="bg-white rounded-lg shadow-sm mb-6">
        <div class="p-5 border-b">
            <h2 class="text-xl font-medium text-gray-900">Filter & Sort</h2>
        </div>
        <div class="p-5">
            <form id="filterForm" action="<?php echo e(route('user.browse.stores')); ?>" method="GET" class="space-y-5">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
                    <div>
                        <label for="category" class="block text-base font-medium text-gray-700 mb-2">Detailed Category</label>
                        <select id="category" name="category" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-2.5">
                            <option value="">All Categories</option>
                            <?php $__currentLoopData = $detailedCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detailedCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($detailedCategory->slug); ?>" <?php echo e($selectedDetailedCategory && $selectedDetailedCategory->id == $detailedCategory->id ? 'selected' : ''); ?>>
                                    <?php echo e($detailedCategory->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div>
                        <label for="sort" class="block text-base font-medium text-gray-700 mb-2">Sort By</label>
                        <select id="sort" name="sort" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-base py-2.5">
                            <option value="popular" <?php echo e(request('sort') == 'popular' || !request('sort') ? 'selected' : ''); ?>>Most Popular</option>
                            <option value="newest" <?php echo e(request('sort') == 'newest' ? 'selected' : ''); ?>>Newest</option>
                            <option value="name_asc" <?php echo e(request('sort') == 'name_asc' ? 'selected' : ''); ?>>Name (A-Z)</option>
                            <option value="name_desc" <?php echo e(request('sort') == 'name_desc' ? 'selected' : ''); ?>>Name (Z-A)</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="bg-purple-600 text-white px-6 py-2.5 rounded-full hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 text-base font-medium">
                            Apply Filters
                        </button>
                    </div>
                </div>
            </form>

            <script>
                // Auto-submit form when select fields change
                document.addEventListener('DOMContentLoaded', function() {
                    const categorySelect = document.getElementById('category');
                    const sortSelect = document.getElementById('sort');

                    categorySelect.addEventListener('change', function() {
                        document.getElementById('filterForm').submit();
                    });

                    sortSelect.addEventListener('change', function() {
                        document.getElementById('filterForm').submit();
                    });
                });
            </script>
        </div>
    </div>

    <?php if($selectedDetailedCategory): ?>
        <div class="mb-6 flex items-center justify-between">
            <div>
                <h2 class="text-xl font-semibold text-gray-900"><?php echo e($selectedDetailedCategory->name); ?> Stores</h2>
                <p class="text-sm text-gray-600">Showing stores with products in the <?php echo e($selectedDetailedCategory->name); ?> detailed category</p>
            </div>
            <a href="<?php echo e(route('user.browse-stores')); ?>" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                Clear Filter
            </a>
        </div>
    <?php endif; ?>

    <?php if(count($formattedStores) > 0): ?>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-8 mb-8">
            <?php $__currentLoopData = $formattedStores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <article class="bg-white rounded-xl border border-gray-100 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
                    <div class="relative">
                        <?php if($store['logo']): ?>
                            <img src="<?php echo e($store['logo']); ?>" alt="<?php echo e($store['name']); ?>" class="w-full h-56 object-cover">
                        <?php else: ?>
                            <div class="w-full h-56 bg-gradient-to-r from-purple-600 to-indigo-600 flex items-center justify-center">
                                <span class="text-7xl font-bold text-white"><?php echo e(substr($store['name'], 0, 1)); ?></span>
                            </div>
                        <?php endif; ?>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 flex items-center">
                            <?php if($store['user_avatar']): ?>
                                <img src="<?php echo e($store['user_avatar']); ?>" alt="<?php echo e($store['user_name']); ?>" class="w-12 h-12 rounded-full border-2 border-white mr-3">
                            <?php else: ?>
                                <div class="w-12 h-12 rounded-full bg-purple-600 text-white flex items-center justify-center font-bold text-xl border-2 border-white mr-3">
                                    <?php echo e(substr($store['user_name'], 0, 1)); ?>

                                </div>
                            <?php endif; ?>
                            <h3 class="text-white font-bold text-xl"><?php echo e($store['name']); ?></h3>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-3">
                            

                            <?php if($selectedDetailedCategory): ?>
                                <!-- When a detailed category is selected, show products in that category -->
                                <span class="text-sm px-3 py-1 bg-purple-100 text-purple-700 rounded-full font-medium">
                                    <?php echo e($store['category_product_count']); ?> <?php echo e(Str::plural('product', $store['category_product_count'])); ?> in <?php echo e($selectedDetailedCategory->name); ?>

                                </span>
                                
                            <?php else: ?>
                                <!-- When viewing all categories, just show the total product count -->
                                <span class="text-sm px-3 py-1 bg-purple-100 text-purple-700 rounded-full font-medium">
                                    <?php echo e($store['product_count']); ?> <?php echo e(Str::plural('product', $store['product_count'])); ?>

                                </span>
                                <?php if($store['main_category']): ?>
                                    <span class="text-gray-600 text-sm font-medium"><?php echo e($store['main_category']->name); ?></span>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                        <p class="text-gray-600 text-base mb-5 line-clamp-2"><?php echo e($store['description']); ?></p>
                        <a href="/<?php echo e($store['slug']); ?>" class="block text-center bg-purple-600 text-white px-6 py-3 rounded-full hover:bg-purple-700 transition-colors duration-300 font-medium">
                            Visit Store
                        </a>
                    </div>
                </article>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            <?php echo e($stores->links()); ?>

        </div>
    <?php else: ?>
        <div class="bg-white rounded-lg shadow-sm p-8 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No stores found</h3>
            <p class="mt-1 text-sm text-gray-500">
                <?php if($selectedDetailedCategory): ?>
                    No stores with products in the <?php echo e($selectedDetailedCategory->name); ?> detailed category were found.
                <?php else: ?>
                    No stores were found matching your criteria.
                <?php endif; ?>
            </p>
            <div class="mt-6">
                <a href="<?php echo e(route('user.browse-stores')); ?>" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    View All Stores
                </a>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user-dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/users/browse-stores.blade.php ENDPATH**/ ?>