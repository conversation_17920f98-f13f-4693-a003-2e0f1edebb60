<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Course;
use App\Models\Order;
use Illuminate\Support\Str;

class ExistingUserCoursePurchaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * This seeder adds course purchases to existing UserSeeder accounts.
     */
    public function run(): void
    {
        $this->command->info('Adding course purchases to existing user accounts...');

        // Get existing users from UserSeeder
        $customerA = User::where('email', '<EMAIL>')->first();
        $customerB = User::where('email', '<EMAIL>')->first();
        $customerC = User::where('email', '<EMAIL>')->first();
        $customerD = User::where('email', '<EMAIL>')->first();

        if (!$customerA || !$customerB || !$customerC || !$customerD) {
            $this->command->error('Some customer accounts not found. Please run UserSeeder first.');
            return;
        }

        // Get all available courses
        $allCourses = Course::where('status', 'active')->get();

        if ($allCourses->isEmpty()) {
            $this->command->error('No active courses found. Please run CourseSeeder first.');
            return;
        }

        $this->command->info("Found {$allCourses->count()} active courses to assign to test users.");

        // Customer A: Has purchased ALL courses (for comprehensive testing)
        $this->command->info('Creating purchases for Customer A (comprehensive testing account)...');
        foreach ($allCourses as $course) {
            $this->createSuccessfulOrder($customerA, $course, "Customer A purchased: {$course->title}");
        }

        // Get specific courses for other customers (maintaining existing test scenarios)
        $whatsappCourse = Course::where('title', 'LIKE', '%WhatsApp Bot%')->first();
        $webDevCourse = Course::where('title', 'LIKE', '%Web Development%')->first();
        $aiCourse = Course::where('title', 'LIKE', '%AI Automation%')->first();
        $marketingCourse = Course::where('title', 'LIKE', '%Digital Marketing%')->first();

        // Customer B: Has purchased only WhatsApp Bot course (limited access testing)
        if ($whatsappCourse) {
            $this->createSuccessfulOrder($customerB, $whatsappCourse, 'Customer B purchased WhatsApp Bot course');
        }

        // Customer C: Has purchased AI Automation course (if available)
        if ($aiCourse) {
            $this->createSuccessfulOrder($customerC, $aiCourse, 'Customer C purchased AI Automation course');
        } elseif ($webDevCourse) {
            // Fallback to Web Development if AI course not available
            $this->createSuccessfulOrder($customerC, $webDevCourse, 'Customer C purchased Web Development course');
        }

        // Customer D: No purchases (for testing access denied flow)
        $this->command->info('Customer D has no purchases (for testing access denied flow)');

        $this->command->info('');
        $this->command->info('🎉 Course purchases added successfully!');
        $this->command->info('');
        $this->command->info('🧪 TESTING SETUP SUMMARY:');
        $this->command->info('=========================');
        $this->command->info('• Customer A: Has purchased ALL courses (comprehensive testing)');
        $this->command->info('• Customer B: Limited access (WhatsApp Bot only)');
        $this->command->info('• Customer C: Single course access (AI Automation or Web Dev)');
        $this->command->info('• Customer D: No purchases (access denied testing)');
        $this->command->info('');
        $this->command->info('📋 TEST ACCOUNT CREDENTIALS:');
        $this->command->info('============================');
        $this->command->info('');
        
        $this->displayUserPurchases($customerA);
        $this->displayUserPurchases($customerB);
        $this->displayUserPurchases($customerC);
        $this->displayUserPurchases($customerD);

        $this->command->info('🔗 TESTING URLS:');
        $this->command->info('================');
        $this->command->info('Login Page: http://digitora.test/login');
        $this->command->info('Course Browse: http://digitora.test/browse/courses');
        $this->command->info('');

        // Display course access URLs for all active courses
        $courses = Course::where('status', 'active')->orderBy('title')->get();

        foreach ($courses as $course) {
            $this->command->info("📚 {$course->title}");
            $this->command->info("   Detail: http://digitora.test/browse/courses/{$course->slug}");
            $this->command->info("   Access: http://digitora.test/browse/courses/{$course->slug}/access");
            $this->command->info('');
        }

        $this->command->info('🧪 TESTING CAPABILITIES ENABLED:');
        $this->command->info('=================================');
        $this->command->info('✓ Course access permissions testing');
        $this->command->info('✓ Curriculum item content display testing');
        $this->command->info('✓ Conditional content delivery testing');
        $this->command->info('✓ Video embed functionality testing');
        $this->command->info('✓ File download functionality testing');
        $this->command->info('✓ Course progress tracking testing');
        $this->command->info('✓ User experience flow testing');
        $this->command->info('');
        $this->command->info('💡 TESTING INSTRUCTIONS:');
        $this->command->info('=========================');
        $this->command->info('1. <NAME_EMAIL> for FULL access testing');
        $this->command->info('2. Visit any course access URL to test curriculum functionality');
        $this->command->info('3. Test different curriculum item types (lecture, video, PDF, document)');
        $this->command->info('4. Verify conditional input fields display correctly');
        $this->command->info('5. Use other customers for limited access scenarios');
        $this->command->info('6. Customer D (no purchases) for access denied testing');
    }

    /**
     * Create a successful order for testing
     */
    private function createSuccessfulOrder($user, $course, $logMessage)
    {
        // Check if order already exists
        $existingOrder = Order::where('buyer_id', $user->id)
            ->where('course_id', $course->id)
            ->where('status', 'success')
            ->first();

        if ($existingOrder) {
            $this->command->info("✓ Order already exists: {$logMessage}");
            return;
        }

        $amount = $course->discount_price > 0 ? $course->discount_price : $course->price;
        
        Order::create([
            'id' => (string) Str::uuid(),
            'order_id' => 'TEST-' . strtoupper(Str::random(8)) . '-' . date('Ymd'),
            'buyer_id' => $user->id,
            'seller_id' => $course->seller_id,
            'course_id' => $course->id,
            'amount' => $amount,
            'status' => 'success',
            'payment_method' => 'test_simulation',
            'snap_token' => null,
            'created_at' => now()->subDays(rand(1, 14)),
            'updated_at' => now()->subDays(rand(1, 14)),
        ]);

        $this->command->info("✓ Created: {$logMessage}");
    }

    /**
     * Display user purchases information
     */
    private function displayUserPurchases($user)
    {
        $this->command->info("👤 {$user->name}");
        $this->command->info("   Email: {$user->email}");
        
        // Extract password from UserSeeder pattern
        $passwordMap = [
            '<EMAIL>' => 'Customer@2024#A!',
            '<EMAIL>' => 'Customer@2024#B!',
            '<EMAIL>' => 'Customer@2024#C!',
            '<EMAIL>' => 'Customer@2024#D!',
        ];
        
        $password = $passwordMap[$user->email] ?? 'Unknown';
        $this->command->info("   Password: {$password}");

        $orders = Order::where('buyer_id', $user->id)
            ->where('status', 'success')
            ->with('course')
            ->get();

        if ($orders->count() > 0) {
            $this->command->info("   Purchased Courses:");
            foreach ($orders as $order) {
                if ($order->course) {
                    $this->command->info("   - {$order->course->title}");
                }
            }
        } else {
            $this->command->info("   Purchased Courses: None (for testing access denied)");
        }
        $this->command->info('');
    }
}
