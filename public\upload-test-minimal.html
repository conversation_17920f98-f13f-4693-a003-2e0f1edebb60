<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Upload Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .upload-area { border: 2px dashed #ccc; padding: 20px; margin: 20px 0; }
        .choose-images, .choose-files { 
            background: #007bff; color: white; padding: 10px 20px; 
            border: none; cursor: pointer; margin: 10px; 
        }
        .hidden { display: none; }
        #log { background: #f0f0f0; padding: 10px; margin: 20px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Minimal Upload Button Test</h1>
    
    <div class="upload-area">
        <p>Images Upload Test</p>
        <input id="images" type="file" class="hidden" accept="image/*" multiple>
        <button type="button" class="choose-images">Choose Images</button>
    </div>
    
    <div class="upload-area">
        <p>Files Upload Test</p>
        <input id="files" type="file" class="hidden" multiple>
        <button type="button" class="choose-files">Choose Files</button>
    </div>
    
    <div id="log"></div>
    
    <script>
        function log(msg) {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + msg + '<br>';
            console.log(msg);
        }
        
        // Test 1: Direct event listeners (should work)
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded - testing direct event listeners');
            
            const chooseImagesBtn = document.querySelector('.choose-images');
            const chooseFilesBtn = document.querySelector('.choose-files');
            
            if (chooseImagesBtn) {
                chooseImagesBtn.addEventListener('click', function() {
                    log('Direct: Images button clicked');
                    const input = document.getElementById('images');
                    if (input) {
                        input.click();
                        log('Direct: Images input triggered');
                    }
                });
            }
            
            if (chooseFilesBtn) {
                chooseFilesBtn.addEventListener('click', function() {
                    log('Direct: Files button clicked');
                    const input = document.getElementById('files');
                    if (input) {
                        input.click();
                        log('Direct: Files input triggered');
                    }
                });
            }
            
            // Test file inputs
            document.getElementById('images').addEventListener('change', function() {
                log('Images selected: ' + this.files.length + ' files');
            });
            
            document.getElementById('files').addEventListener('change', function() {
                log('Files selected: ' + this.files.length + ' files');
            });
            
            log('Direct event listeners attached');
        });
        
        // Test 2: Event delegation (like in product.js)
        document.addEventListener('click', function(event) {
            log('Document click on: ' + event.target.tagName + ' class: ' + event.target.className);
            
            if (event.target.classList.contains('choose-images')) {
                log('Delegation: Images button detected');
                event.preventDefault();
                const input = document.getElementById('images');
                if (input) {
                    input.click();
                    log('Delegation: Images input triggered');
                }
            }
            
            if (event.target.classList.contains('choose-files')) {
                log('Delegation: Files button detected');
                event.preventDefault();
                const input = document.getElementById('files');
                if (input) {
                    input.click();
                    log('Delegation: Files input triggered');
                }
            }
        });
        
        log('Event delegation attached');
    </script>
</body>
</html>