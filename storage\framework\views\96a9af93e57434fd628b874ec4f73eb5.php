<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('title', 'Browse - Digitora'); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/ai-chat.css')); ?>">

    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QWR2LGRD93"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-QWR2LGRD93');
    </script>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>

<body class="font-sans antialiased bg-gray-50">
    <!-- Navigation Header -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="<?php echo e(route('home')); ?>" class="flex items-center gap-2">
                        <div class="flex h-8 w-8 items-center justify-center rounded-md bg-gradient-to-br from-indigo-600 to-purple-700 text-white">
                            <span class="text-lg font-bold">D</span>
                        </div>
                        <span class="text-xl font-bold text-gray-900">Digitora</span>
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="<?php echo e(route('browse.courses')); ?>"
                       class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium <?php echo e(request()->routeIs('browse.courses*') ? 'text-indigo-600 border-b-2 border-indigo-600' : ''); ?>">
                        Courses
                    </a>
                    <a href="<?php echo e(route('browse.products')); ?>"
                       class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium <?php echo e(request()->routeIs('browse.products*') ? 'text-indigo-600 border-b-2 border-indigo-600' : ''); ?>">
                        Products
                    </a>
                    <a href="<?php echo e(route('browse.stores')); ?>"
                       class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium <?php echo e(request()->routeIs('browse.stores*') ? 'text-indigo-600 border-b-2 border-indigo-600' : ''); ?>">
                        Stores
                    </a>
                </div>

                <!-- Right Side Actions -->
                <div class="flex items-center space-x-4">
                    <!-- Cart -->
                    <a href="<?php echo e(route('cart.index')); ?>" class="relative p-2 text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0V19a2 2 0 002 2h7a2 2 0 002-2v-4.5M16 11V7a4 4 0 00-8 0v4"></path>
                        </svg>
                        <?php if(auth()->guard()->check()): ?>
                            <?php
                                $cartItemCount = 0;
                                $cart = \App\Models\Cart::where('user_id', Auth::id())->first();
                                if ($cart) {
                                    $cartItemCount = $cart->items->count();
                                }
                            ?>
                            <?php if($cartItemCount > 0): ?>
                                <span class="absolute -top-1 -right-1 bg-indigo-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                    <?php echo e($cartItemCount); ?>

                                </span>
                            <?php endif; ?>
                        <?php endif; ?>
                    </a>

                    <?php if(auth()->guard()->check()): ?>
                        <!-- User Menu -->
                        <div class="relative">
                            <button type="button" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" id="user-menu-button">
                                <div class="h-8 w-8 rounded-full bg-indigo-600 flex items-center justify-center text-white text-sm font-medium">
                                    <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                                </div>
                            </button>
                            <!-- Dropdown menu would go here -->
                        </div>
                        <a href="<?php echo e(route('user.dashboard')); ?>" 
                           class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            Dashboard
                        </a>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" 
                           class="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium">
                            Login
                        </a>
                        <a href="<?php echo e(route('register')); ?>" 
                           class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            Sign Up
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="min-h-screen">
        <?php if(session('success')): ?>
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
                <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <?php echo e(session('success')); ?>

                </div>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <?php echo e(session('error')); ?>

                </div>
            </div>
        <?php endif; ?>

        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center gap-2 mb-4">
                        <div class="flex h-8 w-8 items-center justify-center rounded-md bg-gradient-to-br from-indigo-600 to-purple-700 text-white">
                            <span class="text-lg font-bold">D</span>
                        </div>
                        <span class="text-xl font-bold">Digitora</span>
                    </div>
                    <p class="text-gray-300 mb-4">
                        Empowering Indonesian entrepreneurs with digital tools, courses, and resources to grow their businesses online.
                    </p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Learn</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="<?php echo e(route('browse.courses')); ?>" class="hover:text-white">Browse Courses</a></li>
                        <li><a href="<?php echo e(route('browse.products')); ?>" class="hover:text-white">Digital Products</a></li>
                        <li><a href="<?php echo e(route('browse.stores')); ?>" class="hover:text-white">Browse Stores</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#" class="hover:text-white">Help Center</a></li>
                        <li><a href="#" class="hover:text-white">Contact Us</a></li>
                        <li><a href="#" class="hover:text-white">Terms of Service</a></li>
                        <li><a href="#" class="hover:text-white">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; <?php echo e(date('Y')); ?> Digitora. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- AI Chat Component -->
    <?php echo $__env->make('components.ai-chat', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- AI Chat JS -->
    <script src="<?php echo e(asset(js_path() . '/ai-chat.js')); ?>" defer></script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

</html>
<?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/layouts/browse.blade.php ENDPATH**/ ?>