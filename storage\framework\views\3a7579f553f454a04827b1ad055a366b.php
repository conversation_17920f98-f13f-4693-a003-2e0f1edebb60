<?php $__env->startSection('content'); ?>
<div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-900">Browse Courses</h1>
    <p class="text-gray-600">Discover online courses designed by Indonesian entrepreneurs</p>
</div>

<!-- Filters and Course Content -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
    <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-900">Filter Courses</h2>
        <span class="text-sm text-gray-500"><?php echo e($courses->total()); ?> courses found</span>
    </div>
    <form method="GET" action="<?php echo e(route('user.browse.courses')); ?>" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <!-- Search -->
            <div class="lg:col-span-2">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text"
                       id="search"
                       name="search"
                       value="<?php echo e(request('search')); ?>"
                       placeholder="Search courses..."
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            </div>

            <!-- Category Filter -->
            <div>
                <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select name="category" id="category" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Categories</option>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                            <?php echo e($category->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>

            <!-- Difficulty Filter -->
            <div>
                <label for="difficulty" class="block text-sm font-medium text-gray-700 mb-1">Difficulty</label>
                <select name="difficulty" id="difficulty" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Levels</option>
                    <option value="beginner" <?php echo e(request('difficulty') == 'beginner' ? 'selected' : ''); ?>>Beginner</option>
                    <option value="intermediate" <?php echo e(request('difficulty') == 'intermediate' ? 'selected' : ''); ?>>Intermediate</option>
                    <option value="advanced" <?php echo e(request('difficulty') == 'advanced' ? 'selected' : ''); ?>>Advanced</option>
                </select>
            </div>

            <!-- Sort -->
            <div>
                <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                <select name="sort" id="sort" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="newest" <?php echo e(request('sort') == 'newest' ? 'selected' : ''); ?>>Newest</option>
                    <option value="popular" <?php echo e(request('sort') == 'popular' ? 'selected' : ''); ?>>Most Popular</option>
                    <option value="price_low" <?php echo e(request('sort') == 'price_low' ? 'selected' : ''); ?>>Price: Low to High</option>
                    <option value="price_high" <?php echo e(request('sort') == 'price_high' ? 'selected' : ''); ?>>Price: High to Low</option>
                </select>
            </div>
        </div>

        <div class="flex justify-end space-x-3">
            <?php if(request()->hasAny(['search', 'category', 'difficulty', 'sort'])): ?>
                <a href="<?php echo e(route('user.browse.courses')); ?>" 
                   class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Clear Filters
                </a>
            <?php endif; ?>
            <button type="submit" 
                    class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                Apply Filters
            </button>
        </div>
    </form>
</div>

<?php if($courses->count() > 0): ?>
    <!-- Course Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
        <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                <!-- Course Thumbnail -->
                <div class="aspect-video bg-gray-200 relative">
                    <?php if($course->thumbnail_url): ?>
                        <img src="<?php echo e($course->thumbnail_url); ?>" alt="<?php echo e($course->title); ?>" class="w-full h-full object-cover">
                    <?php else: ?>
                        <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-indigo-500 to-purple-600">
                            <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Difficulty Badge -->
                    <?php if($course->difficulty_level): ?>
                        <div class="absolute top-2 left-2">
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                <?php echo e($course->difficulty_level === 'beginner' ? 'bg-green-100 text-green-800' : ''); ?>

                                <?php echo e($course->difficulty_level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' : ''); ?>

                                <?php echo e($course->difficulty_level === 'advanced' ? 'bg-red-100 text-red-800' : ''); ?>">
                                <?php echo e(ucfirst($course->difficulty_level)); ?>

                            </span>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Course Content -->
                <div class="p-6">
                    <div class="flex items-center mb-2">
                        <?php if($course->seller && $course->seller->sellerApplication): ?>
                            <div class="flex items-center text-sm text-gray-600">
                                <span><?php echo e($course->seller->sellerApplication->store_name); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                        <a href="<?php echo e(route('browse.courses.show', $course->slug)); ?>" class="hover:text-indigo-600 transition-colors">
                            <?php echo e($course->title); ?>

                        </a>
                    </h3>

                    <?php if($course->short_description): ?>
                        <p class="text-gray-600 text-sm mb-4 line-clamp-2"><?php echo e($course->short_description); ?></p>
                    <?php endif; ?>

                    <!-- Course Stats -->
                    <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                        <div class="flex items-center space-x-4">
                            <span><?php echo e($course->sections_count); ?> sections</span>
                            <span><?php echo e($course->curriculum_items_count); ?> lessons</span>
                        </div>
                    </div>

                    <!-- Price and Action -->
                    <div class="flex items-center justify-between">
                        <div class="text-lg font-bold text-gray-900">
                            Rp <?php echo e(number_format($course->price, 0, ',', '.')); ?>

                        </div>
                        <?php if(in_array($course->id, $purchasedCourseIds ?? [])): ?>
                            <a href="<?php echo e(route('browse.courses.show', $course->slug)); ?>"
                               class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                                Continue Learning
                            </a>
                        <?php else: ?>
                            <a href="<?php echo e(route('browse.courses.show', $course->slug)); ?>"
                               class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors">
                                View Course
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- Pagination -->
    <div class="flex justify-center">
        <?php echo e($courses->appends(request()->query())->links()); ?>

    </div>
<?php else: ?>
    <!-- Empty State -->
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No courses found</h3>
        <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
        <div class="mt-6">
            <a href="<?php echo e(route('user.browse.courses')); ?>" 
               class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                Clear Filters
            </a>
        </div>
    </div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user-dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/users/browse-courses.blade.php ENDPATH**/ ?>