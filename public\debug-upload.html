<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .log { background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: auto; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        .test-btn { background: #007bff; color: white; border: none; }
        .clear-btn { background: #dc3545; color: white; border: none; }
    </style>
</head>
<body>
    <h1>Upload Button Debug Tool</h1>
    
    <div class="debug-section">
        <h3>Debug Console</h3>
        <div id="log" class="log"></div>
        <button class="clear-btn" onclick="clearLog()">Clear Log</button>
    </div>
    
    <div class="debug-section">
        <h3>Element Detection Tests</h3>
        <button class="test-btn" onclick="testElements()">Test Element Existence</button>
        <button class="test-btn" onclick="testEventListeners()">Test Event Listeners</button>
        <button class="test-btn" onclick="testClassNames()">Test Class Names</button>
    </div>
    
    <div class="debug-section">
        <h3>Manual Tests</h3>
        <button class="test-btn" onclick="triggerImagesInput()">Trigger Images Input</button>
        <button class="test-btn" onclick="triggerFilesInput()">Trigger Files Input</button>
        <button class="test-btn" onclick="simulateButtonClick()">Simulate Button Click</button>
    </div>
    
    <script>
        function log(msg) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${msg}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(msg);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function testElements() {
            log('=== ELEMENT DETECTION TEST ===');
            
            // Test for upload inputs
            const imagesInput = document.getElementById('images');
            const filesInput = document.getElementById('files');
            const imageInput = document.getElementById('image');
            
            log(`Images input (#images): ${!!imagesInput}`);
            log(`Files input (#files): ${!!filesInput}`);
            log(`Single image input (#image): ${!!imageInput}`);
            
            // Test for buttons
            const chooseImagesBtn = document.querySelector('.choose-images');
            const chooseFilesBtn = document.querySelector('.choose-files');
            const chooseImageBtn = document.querySelector('.choose-image');
            
            log(`Choose images button (.choose-images): ${!!chooseImagesBtn}`);
            log(`Choose files button (.choose-files): ${!!chooseFilesBtn}`);
            log(`Choose image button (.choose-image): ${!!chooseImageBtn}`);
            
            // Test for upload areas
            const uploadAreas = document.querySelectorAll('.upload-area');
            log(`Upload areas (.upload-area): ${uploadAreas.length} found`);
            
            // Test for forms
            const forms = document.querySelectorAll('form');
            log(`Forms found: ${forms.length}`);
            
            if (chooseImagesBtn) {
                log(`Images button classes: ${chooseImagesBtn.className}`);
                log(`Images button tag: ${chooseImagesBtn.tagName}`);
                log(`Images button type: ${chooseImagesBtn.type || 'N/A'}`);
            }
            
            if (chooseFilesBtn) {
                log(`Files button classes: ${chooseFilesBtn.className}`);
                log(`Files button tag: ${chooseFilesBtn.tagName}`);
                log(`Files button type: ${chooseFilesBtn.type || 'N/A'}`);
            }
        }
        
        function testEventListeners() {
            log('=== EVENT LISTENER TEST ===');
            
            // Test document click listener
            log('Testing document click listener...');
            document.addEventListener('click', function(e) {
                log(`Document click: ${e.target.tagName}.${e.target.className}`);
            }, { once: true });
            
            // Test if we can add listeners to buttons
            const chooseImagesBtn = document.querySelector('.choose-images');
            const chooseFilesBtn = document.querySelector('.choose-files');
            
            if (chooseImagesBtn) {
                chooseImagesBtn.addEventListener('click', function(e) {
                    log('✅ Images button click listener triggered!');
                    e.preventDefault();
                }, { once: true });
                log('Added test listener to images button');
            }
            
            if (chooseFilesBtn) {
                chooseFilesBtn.addEventListener('click', function(e) {
                    log('✅ Files button click listener triggered!');
                    e.preventDefault();
                }, { once: true });
                log('Added test listener to files button');
            }
        }
        
        function testClassNames() {
            log('=== CLASS NAME TEST ===');
            
            const allElements = document.querySelectorAll('*');
            const relevantElements = [];
            
            allElements.forEach(el => {
                if (el.className && typeof el.className === 'string') {
                    if (el.className.includes('choose') || el.className.includes('upload') || el.className.includes('file') || el.className.includes('image')) {
                        relevantElements.push({
                            tag: el.tagName,
                            classes: el.className,
                            id: el.id || 'N/A'
                        });
                    }
                }
            });
            
            log(`Found ${relevantElements.length} elements with relevant classes:`);
            relevantElements.forEach(el => {
                log(`  ${el.tag}#${el.id}.${el.classes}`);
            });
        }
        
        function triggerImagesInput() {
            log('=== MANUAL IMAGES INPUT TRIGGER ===');
            const imagesInput = document.getElementById('images');
            if (imagesInput) {
                log('Triggering images input click...');
                imagesInput.click();
                log('Images input click triggered');
            } else {
                log('❌ Images input not found');
            }
        }
        
        function triggerFilesInput() {
            log('=== MANUAL FILES INPUT TRIGGER ===');
            const filesInput = document.getElementById('files');
            if (filesInput) {
                log('Triggering files input click...');
                filesInput.click();
                log('Files input click triggered');
            } else {
                log('❌ Files input not found');
            }
        }
        
        function simulateButtonClick() {
            log('=== SIMULATE BUTTON CLICK ===');
            const chooseImagesBtn = document.querySelector('.choose-images');
            if (chooseImagesBtn) {
                log('Simulating click on images button...');
                chooseImagesBtn.click();
                log('Button click simulated');
            } else {
                log('❌ Images button not found');
            }
        }
        
        // Auto-run basic tests on load
        window.addEventListener('load', function() {
            log('=== DEBUG TOOL LOADED ===');
            log('Ready to debug upload functionality');
            log('Click the test buttons above to run diagnostics');
        });
        
        // Monitor all clicks
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.debug-section')) {
                log(`🖱️ Click detected: ${e.target.tagName}.${e.target.className}`);
            }
        });
    </script>
</body>
</html>