<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div class="min-w-0 flex-1">
            <h1 class="text-2xl sm:text-3xl font-bold tracking-tight text-gray-900">My Courses</h1>
            <p class="text-gray-600 mt-1">Manage your online courses and educational content</p>
        </div>
        <div class="flex-shrink-0">
            <a href="<?php echo e(route('seller.courses.create')); ?>"
               class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 transition-colors w-full sm:w-auto justify-center">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                <span class="whitespace-nowrap">Create New Course</span>
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
        <form method="GET" action="<?php echo e(route('seller.courses.index')); ?>" class="space-y-4">
            <!-- Search - Full width on mobile -->
            <div class="w-full">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <input type="text"
                       id="search"
                       name="search"
                       value="<?php echo e(request('search')); ?>"
                       placeholder="Search courses..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
            </div>

            <!-- Filters in responsive grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="w-full">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="status"
                            name="status"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                        <option value="all" <?php echo e(request('status') === 'all' ? 'selected' : ''); ?>>All Status</option>
                        <option value="draft" <?php echo e(request('status') === 'draft' ? 'selected' : ''); ?>>Draft</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                    </select>
                </div>

                <div class="w-full">
                    <label for="difficulty" class="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
                    <select id="difficulty"
                            name="difficulty"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                        <option value="all" <?php echo e(request('difficulty') === 'all' ? 'selected' : ''); ?>>All Levels</option>
                        <option value="beginner" <?php echo e(request('difficulty') === 'beginner' ? 'selected' : ''); ?>>Beginner</option>
                        <option value="intermediate" <?php echo e(request('difficulty') === 'intermediate' ? 'selected' : ''); ?>>Intermediate</option>
                        <option value="advanced" <?php echo e(request('difficulty') === 'advanced' ? 'selected' : ''); ?>>Advanced</option>
                    </select>
                </div>

                <div class="w-full sm:col-span-2 lg:col-span-1">
                    <label class="block text-sm font-medium text-gray-700 mb-2">&nbsp;</label>
                    <button type="submit"
                            class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm">
                        Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Courses List -->
    <div class="rounded-lg border bg-white shadow-sm">
        <div class="flex flex-col gap-4 border-b p-6 sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h3 class="text-lg font-medium">Course Management</h3>
                <p class="text-sm text-gray-500">Manage and track your online courses</p>
            </div>
        </div>
        <div class="table-container overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr>
                        <th>Course</th>
                        <th class="hidden md:table-cell">Category</th>
                        <th class="text-right">Price</th>
                        <th>Status</th>
                        <th class="hidden sm:table-cell">Content</th>
                        <th class="hidden lg:table-cell">Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $courses ?? collect(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr class="hover:bg-gray-50 transition-colors">
                        <td>
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <img class="h-10 w-10 rounded-lg object-cover"
                                         src="<?php echo e($course->thumbnail_url); ?>"
                                         alt="<?php echo e($course->title); ?>">
                                </div>
                                <div class="ml-3 min-w-0 flex-1">
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($course->title); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo e(Str::limit($course->short_description, 40)); ?></div>
                                    <!-- Mobile-only stacked info -->
                                    <div class="md:hidden text-xs text-gray-500 mt-1">
                                        <?php echo e($course->detailedCategory->name ?? 'Uncategorized'); ?> • <?php echo e($course->difficulty_level); ?>

                                    </div>
                                    <div class="sm:hidden text-xs text-gray-500 mt-1">
                                        <?php echo e($course->chapters_count); ?> chapters • <?php echo e($course->materials_count); ?> materials
                                    </div>
                                    <div class="lg:hidden text-xs text-gray-500 mt-1">
                                        Created <?php echo e($course->created_at->format('M d, Y')); ?>

                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="hidden md:table-cell">
                            <div class="text-sm text-gray-900"><?php echo e($course->detailedCategory->name ?? 'Uncategorized'); ?></div>
                            <div class="text-sm text-gray-500"><?php echo e($course->difficulty_level); ?></div>
                        </td>
                        <td class="text-right font-medium">
                            <div class="text-sm font-medium text-gray-900">
                                Rp <?php echo e(number_format($course->effective_price)); ?>

                            </div>
                            <?php if($course->has_discount): ?>
                                <div class="text-xs text-gray-500 line-through">
                                    Rp <?php echo e(number_format($course->price)); ?>

                                </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="status-label inline-flex items-center justify-center
                                <?php echo e($course->status === 'active' ? 'success' :
                                   ($course->status === 'draft' ? 'pending' : 'cancel')); ?>">
                                <?php echo e(ucfirst($course->status)); ?>

                            </span>
                        </td>
                        <td class="hidden sm:table-cell">
                            <div class="text-sm text-gray-500"><?php echo e($course->chapters_count); ?> chapters</div>
                            <div class="text-sm text-gray-500"><?php echo e($course->materials_count); ?> materials</div>
                        </td>
                        <td class="whitespace-nowrap hidden lg:table-cell"><?php echo e($course->created_at->format('M d, Y')); ?></td>
                        <td>
                            <div class="flex flex-col space-y-1">
                                <a href="<?php echo e(route('seller.courses.show', $course)); ?>"
                                   class="text-indigo-600 hover:text-indigo-900 font-medium transition-colors text-xs">Manage</a>
                                <a href="<?php echo e(route('seller.courses.edit', $course)); ?>"
                                   class="text-blue-600 hover:text-blue-900 transition-colors text-xs">Edit</a>
                                <?php if($course->status === 'active'): ?>
                                    <a href="<?php echo e(route('browse.courses.show', $course)); ?>"
                                       class="text-purple-600 hover:text-purple-900 transition-colors text-xs"
                                       target="_blank"
                                       title="Preview how your course appears to students">
                                        Preview
                                    </a>
                                <?php endif; ?>
                                <form action="<?php echo e(route('seller.courses.toggle-status', $course)); ?>"
                                      method="POST"
                                      class="inline">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit"
                                            class="text-<?php echo e($course->status === 'active' ? 'red' : 'green'); ?>-600 hover:text-<?php echo e($course->status === 'active' ? 'red' : 'green'); ?>-900 transition-colors text-xs text-left">
                                        <?php echo e($course->status === 'active' ? 'Deactivate' : 'Activate'); ?>

                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="7" class="text-center text-gray-500 py-8">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <p>No courses found</p>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <div class="border-t px-6 py-4">
            <?php echo e(($courses ?? collect())->links()); ?>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('seller.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/seller/courses/index.blade.php ENDPATH**/ ?>