<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Button Debug Test</title>
    <style>
        .upload-area {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px;
            text-align: center;
        }
        .choose-images, .choose-files {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            cursor: pointer;
            margin: 10px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>Upload Button Debug Test</h1>
    
    <!-- Images Upload Section -->
    <div class="upload-area">
        <h3>Images Upload</h3>
        <input id="images" name="images[]" type="file" class="hidden" accept="image/*" multiple>
        <button type="button" class="choose-images">Choose Images</button>
    </div>
    
    <!-- Files Upload Section -->
    <div class="upload-area">
        <h3>Files Upload</h3>
        <input id="files" name="files[]" type="file" class="hidden" multiple>
        <button type="button" class="choose-files">Choose Files</button>
    </div>
    
    <div id="debug-log" style="margin: 20px; padding: 10px; background: #f0f0f0; font-family: monospace;"></div>
    
    <script>
        function log(message) {
            const debugLog = document.getElementById('debug-log');
            debugLog.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
            console.log(message);
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM Content Loaded');
            
            // Test if elements exist
            const imagesInput = document.getElementById('images');
            const filesInput = document.getElementById('files');
            const chooseImagesBtn = document.querySelector('.choose-images');
            const chooseFilesBtn = document.querySelector('.choose-files');
            
            log('Images input exists: ' + !!imagesInput);
            log('Files input exists: ' + !!filesInput);
            log('Choose images button exists: ' + !!chooseImagesBtn);
            log('Choose files button exists: ' + !!chooseFilesBtn);
            
            // Event delegation (same as in product.js)
            document.addEventListener('click', function(event) {
                log('Document click detected on: ' + event.target.tagName + ' with classes: ' + event.target.className);
                
                function hasClass(element, className) {
                    return element && element.classList && element.classList.contains(className);
                }

                function findParentWithClass(element, className) {
                    let current = element;
                    while (current && current !== document) {
                        if (hasClass(current, className)) {
                            return current;
                        }
                        current = current.parentElement;
                    }
                    return null;
                }

                // Handle choose-images buttons
                if (hasClass(event.target, 'choose-images') || findParentWithClass(event.target, 'choose-images')) {
                    log('Choose images button clicked!');
                    event.preventDefault();
                    event.stopPropagation();
                    const imagesInput = document.getElementById("images");
                    if (imagesInput) {
                        log('Triggering images input click');
                        imagesInput.click();
                    } else {
                        log('Images input not found!');
                    }
                    return;
                }

                // Handle choose-files buttons
                if (hasClass(event.target, 'choose-files') || findParentWithClass(event.target, 'choose-files')) {
                    log('Choose files button clicked!');
                    event.preventDefault();
                    event.stopPropagation();
                    const fileInput = document.getElementById("files");
                    if (fileInput) {
                        log('Triggering files input click');
                        fileInput.click();
                    } else {
                        log('Files input not found!');
                    }
                    return;
                }
            });
            
            // Test file input change events
            if (imagesInput) {
                imagesInput.addEventListener('change', function() {
                    log('Images input changed - files selected: ' + this.files.length);
                });
            }
            
            if (filesInput) {
                filesInput.addEventListener('change', function() {
                    log('Files input changed - files selected: ' + this.files.length);
                });
            }
            
            log('Event listeners attached successfully');
        });
    </script>
</body>
</html>