<?php $__env->startSection('content'); ?>
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <h1 class="text-2xl font-bold text-gray-900">My Purchases</h1>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">View all your purchased products</p>
        </div>

        <div class="border-t border-gray-200">
            <div class="bg-white px-4 py-5 sm:p-6">
                <?php if(count($purchases ?? []) > 0): ?>
                    <ul role="list" class="divide-y divide-gray-200">
                        <?php $__currentLoopData = $purchases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $purchase): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="py-4 flex">
                                <div class="flex-shrink-0 h-16 w-16 border border-gray-200 rounded-md overflow-hidden">
                                    <?php if($purchase->isCourseOrder() && $purchase->course): ?>
                                        <img src="<?php echo e($purchase->course->thumbnail ? asset('storage/' . $purchase->course->thumbnail) : asset('images/placeholder.jpg')); ?>"
                                            alt="<?php echo e($purchase->course->title); ?>"
                                            class="h-full w-full object-center object-cover">
                                    <?php elseif($purchase->isProductOrder() && $purchase->product): ?>
                                        <img src="<?php echo e($purchase->product->image ? asset('storage/' . $purchase->product->image) : asset('images/placeholder.jpg')); ?>"
                                            alt="<?php echo e($purchase->product->name); ?>"
                                            class="h-full w-full object-center object-cover">
                                    <?php else: ?>
                                        <img src="<?php echo e(asset('images/placeholder.jpg')); ?>"
                                            alt="Unknown Item"
                                            class="h-full w-full object-center object-cover">
                                    <?php endif; ?>
                                </div>
                                <div class="ml-4 flex-1 flex flex-col">
                                    <div>
                                        <div class="flex justify-between text-base font-medium text-gray-900">
                                            <h3><?php echo e($purchase->item_name); ?></h3>
                                            <p class="ml-4">Rp <?php echo e(number_format($purchase->amount, 0, ',', '.')); ?></p>
                                        </div>
                                        <div class="mt-1 flex items-center gap-2">
                                            <span class="text-sm text-gray-500">
                                                <?php if($purchase->isCourseOrder() && $purchase->course): ?>
                                                    <?php if($purchase->course->detailedCategory): ?>
                                                        <?php echo e($purchase->course->detailedCategory->name); ?>

                                                    <?php elseif($purchase->course->subcategory): ?>
                                                        <?php echo e($purchase->course->subcategory->name); ?>

                                                    <?php elseif($purchase->course->category): ?>
                                                        <?php echo e($purchase->course->category->name); ?>

                                                    <?php else: ?>
                                                        Course
                                                    <?php endif; ?>
                                                <?php elseif($purchase->isProductOrder() && $purchase->product): ?>
                                                    <?php if($purchase->product->productDetailedCategory): ?>
                                                        <?php echo e($purchase->product->productDetailedCategory->name); ?>

                                                    <?php elseif($purchase->product->productSubcategory): ?>
                                                        <?php echo e($purchase->product->productSubcategory->name); ?>

                                                    <?php elseif($purchase->product->productCategory): ?>
                                                        <?php echo e($purchase->product->productCategory->name); ?>

                                                    <?php else: ?>
                                                        <?php echo e($purchase->product->category); ?>

                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    Unknown Category
                                                <?php endif; ?>
                                            </span>
                                            <?php if($purchase->isCourseOrder()): ?>
                                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                                    </svg>
                                                    Course
                                                </span>
                                            <?php else: ?>
                                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                    Digital Product
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="flex-1 flex items-end justify-between text-sm">
                                        <p class="text-gray-500">Purchased on <?php echo e($purchase->created_at->format('M d, Y')); ?>

                                        </p>
                                        <?php if($purchase->isCourseOrder() && $purchase->course): ?>
                                            <a href="<?php echo e(route('browse.courses.access', $purchase->course)); ?>"
                                               class="inline-flex items-center px-3 py-1.5 bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium rounded-lg transition-colors">
                                                <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                                </svg>
                                                Access Course
                                            </a>
                                        <?php elseif($purchase->isProductOrder() && $purchase->product): ?>
                                            <form action="<?php echo e(route('product.download', $purchase->product->id)); ?>"
                                                method="POST" class="inline-block">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit"
                                                    class="inline-flex items-center px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">
                                                    <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-4-4m4 4l4-4m-6 4h8"></path>
                                                    </svg>
                                                    Download
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <span class="text-gray-400 text-sm">Item unavailable</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>

                    <div class="mt-6 text-center">
                        <a href="<?php echo e(route('user.browse.courses')); ?>"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Browse More Products
                        </a>
                    </div>
                <?php else: ?>
                    <div class="text-center py-12">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No purchases yet</h3>
                        <p class="mt-1 text-sm text-gray-500">Get started by exploring our products.</p>
                        <div class="mt-6">
                            <a href="<?php echo e(route('user.browse')); ?>"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Browse Products
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="mt-16 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl p-8 text-center text-white">
        <h2 class="text-2xl font-bold mb-4">Ready to Start Selling Your Digital Products?</h2>
        <p class="mb-6 max-w-2xl mx-auto">Join thousands of creators who are earning by selling their digital products
            on Digitora.</p>
        <a href="<?php echo e(route('seller.apply')); ?>"
            class="inline-block bg-white text-purple-600 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors duration-300">
            Become a Seller Today
        </a>
    </div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user-dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\digitora\resources\views/users/purchases.blade.php ENDPATH**/ ?>